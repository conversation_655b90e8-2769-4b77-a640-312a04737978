#!/usr/bin/env python3
"""
Debug script to help understand timing issues in get_git_diff.
This script will help identify what type of question is being processed
and what timing information should be expected.
"""

import json
import sys

def analyze_timing_output(timing_json_str):
    """
    Analyze the timing output to understand what's missing
    """
    try:
        timing_data = json.loads(timing_json_str)
        
        print("=" * 60)
        print("TIMING DATA ANALYSIS")
        print("=" * 60)
        
        # Check if it has the expected structure
        if 'step_timings' in timing_data:
            print("✓ Found step_timings")
            step_timings = timing_data['step_timings']
            
            print("\nStep Timings Found:")
            for step, duration in step_timings.items():
                print(f"  - {step}: {duration} ms")
                
        else:
            print("✗ Missing step_timings")
            
        if 'grading_details' in timing_data:
            print("\n✓ Found grading_details")
            grading_details = timing_data['grading_details']
            
            # Check for detailed grading information
            if 'marking_points' in grading_details:
                mp_count = len(grading_details['marking_points'])
                print(f"  - Marking points processed: {mp_count}")
                
            if 'pinecone_queries' in grading_details:
                pq_count = len(grading_details['pinecone_queries'])
                print(f"  - Pinecone queries made: {pq_count}")
                
            if 'llm_evaluations' in grading_details:
                llm_count = len(grading_details['llm_evaluations'])
                print(f"  - LLM evaluations performed: {llm_count}")
                
            if 'summary' in grading_details:
                summary = grading_details['summary']
                print(f"  - Summary statistics available: {list(summary.keys())}")
                
        else:
            print("\n✗ Missing grading_details")
            
        # Provide recommendations
        print("\n" + "=" * 60)
        print("RECOMMENDATIONS")
        print("=" * 60)
        
        if 'grading_details' not in timing_data or not timing_data['grading_details']:
            print("🔍 ISSUE: Missing detailed grading information")
            print("\nPossible causes:")
            print("1. Question is MCQ type (MCQs have simpler timing)")
            print("2. Question has no marking points")
            print("3. Error occurred during grading process")
            print("4. Timing data not being properly returned from helper function")
            
            print("\nTo debug:")
            print("1. Check the application logs for question type")
            print("2. Verify the question has marking points")
            print("3. Look for any error messages in the logs")
            
        else:
            print("✓ Timing data looks complete!")
            
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON: {e}")
        print("Make sure you're providing valid JSON timing data")

def show_expected_structure():
    """
    Show what the complete timing structure should look like
    """
    print("=" * 60)
    print("EXPECTED TIMING STRUCTURE")
    print("=" * 60)
    
    expected = {
        "step_timings": {
            "initialization": "Time to setup Gemini & Pinecone",
            "input_processing": "Time to process user input",
            "ocr_processing": "Time for OCR (if image submitted)",
            "grading": "Total grading time",
            "highlighting": "Time for highlighting",
            "highlighting_llm": "Time for LLM highlighting call",
            "database_operations": "Time for DB operations",
            "total_time": "Total processing time"
        },
        "grading_details": {
            "free_response_processing": "Time for free-response grading",
            "marking_points": [
                {
                    "marking_point_id": "ID of marking point",
                    "total_duration_ms": "Total time for this MP",
                    "achieved_score": "Score achieved",
                    "max_score": "Maximum possible score"
                }
            ],
            "pinecone_queries": [
                {
                    "marking_point_id": "ID of marking point",
                    "duration_ms": "Time for Pinecone query",
                    "context_retrieved": "Whether context was found"
                }
            ],
            "llm_evaluations": [
                {
                    "marking_point_id": "ID of marking point", 
                    "duration_ms": "Time for LLM evaluation",
                    "result": "correct/partial/incorrect",
                    "error": "Whether there was an error"
                }
            ],
            "summary": {
                "total_marking_points": "Number of marking points",
                "total_pinecone_time_ms": "Total Pinecone time",
                "total_llm_time_ms": "Total LLM time",
                "average_mp_time_ms": "Average time per marking point"
            }
        }
    }
    
    print(json.dumps(expected, indent=2))

def main():
    """
    Main function
    """
    print("Timing Debug Tool")
    print("================")
    
    if len(sys.argv) > 1:
        # Analyze provided timing data
        timing_json = sys.argv[1]
        analyze_timing_output(timing_json)
    else:
        # Show expected structure
        show_expected_structure()
        print("\n" + "=" * 60)
        print("USAGE")
        print("=" * 60)
        print("To analyze actual timing data:")
        print('python debug_timing.py \'{"step_timings": {...}, "grading_details": {...}}\'')
        print("\nOr just run without arguments to see expected structure.")

if __name__ == "__main__":
    main()
